{"name": "@stellaris/vite-plugin-tencent-oss", "version": "0.1.2", "author": "<PERSON><PERSON><PERSON>i", "description": "Upload the production files bundled in the project to Tencent CSS, except for html", "homepage": "https://github.com/taosiqi/vite-plugin-tencent-oss", "repository": "https://github.com/taosiqi/vite-plugin-tencent-oss", "license": "MIT", "type": "module", "main": "index.js", "keywords": ["vite-plugin", "tencent-oss", "tencent-css", "tencent", "腾讯云", "腾讯云对象存储"], "scripts": {"example:build": "pnpm -C example build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "dependencies": {"await-to-js": "^3.0.0", "conventional-changelog-cli": "^2.2.2", "cos-nodejs-sdk-v5": "^2.11.12", "glob": "^10.0.0", "picocolors": "^1.0.0"}, "peerDependencies": {"vite": "^2"}, "publishConfig": {"@taosiqi:registry": "https://npm.pkg.github.com"}, "engines": {"node": ">=12.0.0"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}